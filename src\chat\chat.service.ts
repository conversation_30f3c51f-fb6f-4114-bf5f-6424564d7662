import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, More<PERSON>han, LessThan, In } from 'typeorm';
import { Chat, ChatType } from './entity/chat.entity';
import { Message, MessageType } from './entity/message.entity';
import { MessageReadStatus } from './entity/message-read-status.entity';
import { User } from '../user/entity/user.entity';
import { CreateChatDto } from './dto/create-chat.dto';
import { SendMessageDto } from './dto/send-message.dto';
import { ChatQueryDto, MessageQueryDto } from './dto/chat-query.dto';
import * as DOMPurify from 'dompurify';
import { JSDOM } from 'jsdom';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);
  private readonly domPurify: any;

  constructor(
    @InjectRepository(Chat)
    private chatRepository: Repository<Chat>,
    @InjectRepository(Message)
    private messageRepository: Repository<Message>,
    @InjectRepository(MessageReadStatus)
    private messageReadStatusRepository: Repository<MessageReadStatus>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    // Initialize DOMPurify for server-side sanitization
    const window = new JSDOM('').window;
    this.domPurify = DOMPurify(window as any);
  }

  async createChat(
    createChatDto: CreateChatDto,
    creatorId: number,
  ): Promise<Chat> {
    const { type, name, description, participantIds } = createChatDto;

    // Validate chat creation rules
    if (type === ChatType.DIRECT && participantIds.length !== 1) {
      throw new BadRequestException(
        'Direct chats must have exactly one other participant',
      );
    }

    if (type === ChatType.GROUP && !name) {
      throw new BadRequestException('Group chats must have a name');
    }

    // Include creator in participants
    const allParticipantIds = [...new Set([creatorId, ...participantIds])];

    // Validate participants exist
    const participants = await this.userRepository.findBy({
      id: In(allParticipantIds),
    });

    if (participants.length !== allParticipantIds.length) {
      throw new BadRequestException('One or more participants not found');
    }

    // Check if direct chat already exists
    if (type === ChatType.DIRECT) {
      const existingChat = await this.findExistingDirectChat(
        creatorId,
        participantIds[0],
      );
      if (existingChat) {
        return existingChat;
      }
    }

    // Create new chat
    const chat = this.chatRepository.create({
      type,
      name: name ? this.sanitizeContent(name) : undefined,
      description: description ? this.sanitizeContent(description) : undefined,
      participants,
    });

    return this.chatRepository.save(chat);
  }

  async getUserChats(
    userId: number,
    query: ChatQueryDto,
  ): Promise<{ chats: Chat[]; hasMore: boolean; nextCursor?: string }> {
    const { limit = 20, cursor } = query;

    // First, find chat IDs where the user is a participant
    let chatIdsQuery = this.chatRepository
      .createQueryBuilder('chat')
      .select('chat.id')
      .leftJoin('chat.participants', 'participant')
      .where('participant.id = :userId', { userId })
      .orderBy('chat.lastMessageAt', 'DESC')
      .addOrderBy('chat.updatedAt', 'DESC')
      .take(limit + 1);

    if (cursor) {
      chatIdsQuery = chatIdsQuery.andWhere('chat.id < :cursor', { cursor });
    }

    const chatIds = await chatIdsQuery.getRawMany();

    if (chatIds.length === 0) {
      return { chats: [], hasMore: false };
    }

    const hasMore = chatIds.length > limit;
    if (hasMore) {
      chatIds.pop(); // Remove the extra item
    }

    // Now fetch the complete chat data with all participants
    const chats = await this.chatRepository.find({
      where: { id: In(chatIds.map(c => c.chat_id)) },
      relations: ['participants'],
      order: { lastMessageAt: 'DESC', updatedAt: 'DESC' },
    });

    // For each chat, get the last message with read statuses
    const chatsWithLastMessage = await Promise.all(
      chats.map(async (chat) => {
        const lastMessage = await this.messageRepository.findOne({
          where: { chat: { id: chat.id } },
          relations: ['sender', 'readStatuses', 'readStatuses.user'],
          order: { createdAt: 'DESC' },
        });

        return {
          ...chat,
          messages: lastMessage ? [lastMessage] : [],
        };
      })
    );

    // Sort chats to match the original order from the query
    const sortedChats = chatIds.map(chatIdObj =>
      chatsWithLastMessage.find(chat => chat.id === chatIdObj.chat_id)
    ).filter((chat): chat is Chat => chat !== undefined);

    const nextCursor = hasMore ? sortedChats[sortedChats.length - 1]?.id : undefined;

    return { chats: sortedChats, hasMore, nextCursor };
  }

  async getChatMessages(
    chatId: string,
    userId: number,
    query: MessageQueryDto,
  ): Promise<{ messages: Message[]; hasMore: boolean; nextCursor?: string }> {
    // Verify user has access to this chat
    await this.validateChatAccess(chatId, userId);

    const { limit = 50, cursor } = query;

    const queryBuilder = this.messageRepository
      .createQueryBuilder('message')
      .leftJoinAndSelect('message.sender', 'sender')
      .leftJoinAndSelect('message.readStatuses', 'readStatuses')
      .leftJoinAndSelect('readStatuses.user', 'readUser')
      .where('message.chat.id = :chatId', { chatId })
      .orderBy('message.createdAt', 'DESC')
      .take(limit + 1);

    if (cursor) {
      queryBuilder.andWhere('message.id < :cursor', { cursor });
    }

    const messages = await queryBuilder.getMany();
    const hasMore = messages.length > limit;

    if (hasMore) {
      messages.pop();
    }

    // Reverse to get chronological order (oldest first)
    messages.reverse();

    const nextCursor = hasMore ? messages[0]?.id : undefined;

    return { messages, hasMore, nextCursor };
  }

  async sendMessage(
    chatId: string,
    senderId: number,
    sendMessageDto: SendMessageDto,
  ): Promise<Message> {
    // Verify user has access to this chat
    const chat = await this.validateChatAccess(chatId, senderId);

    const sender = await this.userRepository.findOne({
      where: { id: senderId },
    });

    if (!sender) {
      throw new NotFoundException('Sender not found');
    }

    // Sanitize message content
    const sanitizedContent = this.sanitizeContent(sendMessageDto.content);

    // Create message
    const message = this.messageRepository.create({
      chat,
      sender,
      content: sanitizedContent,
      type: sendMessageDto.type,
    });

    const savedMessage = await this.messageRepository.save(message);

    // Update chat's last message timestamp
    await this.chatRepository.update(chatId, {
      lastMessageAt: savedMessage.createdAt,
    });

    // Create read status for all participants except sender
    const readStatuses = chat.participants
      .filter((participant) => participant.id !== senderId)
      .map((participant) =>
        this.messageReadStatusRepository.create({
          message: savedMessage,
          user: participant,
          readAt: undefined, // Not read yet
        }),
      );

    if (readStatuses.length > 0) {
      await this.messageReadStatusRepository.save(readStatuses);
    }

    // Load the complete message with relations
    const completeMessage = await this.messageRepository.findOne({
      where: { id: savedMessage.id },
      relations: ['sender', 'readStatuses', 'readStatuses.user'],
    });

    if (!completeMessage) {
      throw new Error('Failed to load saved message');
    }

    return completeMessage;
  }

  async markMessagesAsRead(
    chatId: string,
    userId: number,
  ): Promise<{ markedCount: number }> {
    // Verify user has access to this chat
    await this.validateChatAccess(chatId, userId);

    // Find unread message read statuses for this user in this chat
    const unreadStatuses = await this.messageReadStatusRepository
      .createQueryBuilder('readStatus')
      .leftJoinAndSelect('readStatus.message', 'message')
      .where('message.chat.id = :chatId', { chatId })
      .andWhere('readStatus.user.id = :userId', { userId })
      .andWhere('readStatus.readAt IS NULL')
      .getMany();

    if (unreadStatuses.length === 0) {
      return { markedCount: 0 };
    }

    // Mark all as read
    const now = new Date();
    await this.messageReadStatusRepository.update(
      { id: In(unreadStatuses.map((status) => status.id)) },
      { readAt: now },
    );

    return { markedCount: unreadStatuses.length };
  }

  async getUnreadMessageCount(
    chatId: string,
    userId: number,
  ): Promise<number> {
    return this.messageReadStatusRepository
      .createQueryBuilder('readStatus')
      .leftJoin('readStatus.message', 'message')
      .leftJoin('message.chat', 'chat')
      .where('chat.id = :chatId', { chatId })
      .andWhere('readStatus.user.id = :userId', { userId })
      .andWhere('readStatus.readAt IS NULL')
      .getCount();
  }

  async validateChatAccess(
    chatId: string,
    userId: number,
  ): Promise<Chat> {
    const chat = await this.chatRepository.findOne({
      where: { id: chatId },
      relations: ['participants'],
    });

    if (!chat) {
      throw new NotFoundException('Chat not found');
    }

    if (!chat.isParticipant(userId)) {
      throw new ForbiddenException('You are not a participant in this chat');
    }

    return chat;
  }

  private async findExistingDirectChat(
    userId1: number,
    userId2: number,
  ): Promise<Chat | null> {
    // Find all direct chats that include userId1
    const chats = await this.chatRepository.find({
      where: { type: ChatType.DIRECT },
      relations: ['participants'],
    });

    // Check if any of these chats has exactly the two users we're looking for
    for (const chat of chats) {
      const participantIds = chat.participants.map(p => p.id);
      if (
        participantIds.length === 2 &&
        participantIds.includes(userId1) &&
        participantIds.includes(userId2)
      ) {
        return chat;
      }
    }

    return null;
  }

  private sanitizeContent(content: string): string {
    // Remove any HTML tags and sanitize content
    return this.domPurify.sanitize(content, { ALLOWED_TAGS: [] });
  }
}

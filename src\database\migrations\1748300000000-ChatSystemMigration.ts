import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChatSystemMigration1748300000000 implements MigrationInterface {
  name = 'ChatSystemMigration1748300000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create chat table
    await queryRunner.query(`
      CREATE TABLE "chat" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "type" character varying NOT NULL DEFAULT 'direct',
        "name" character varying,
        "description" character varying,
        "lastMessageAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_9d0b2ba74336710fd31154738a5" PRIMARY KEY ("id")
      )
    `);

    // Create message table
    await queryRunner.query(`
      CREATE TABLE "message" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "content" text NOT NULL,
        "type" character varying NOT NULL DEFAULT 'text',
        "status" character varying NOT NULL DEFAULT 'sent',
        "editedAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        "chatId" uuid,
        "senderId" integer,
        CONSTRAINT "PK_ba01f0a3e0123651915008bc578" PRIMARY KEY ("id")
      )
    `);

    // Create message_read_status table
    await queryRunner.query(`
      CREATE TABLE "message_read_status" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "readAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "messageId" uuid,
        "userId" integer,
        CONSTRAINT "UQ_message_user" UNIQUE ("messageId", "userId"),
        CONSTRAINT "PK_message_read_status" PRIMARY KEY ("id")
      )
    `);

    // Create chat_participants junction table
    await queryRunner.query(`
      CREATE TABLE "chat_participants" (
        "chatId" uuid NOT NULL,
        "userId" integer NOT NULL,
        CONSTRAINT "PK_chat_participants" PRIMARY KEY ("chatId", "userId")
      )
    `);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_chat_lastMessageAt" ON "chat" ("lastMessageAt" DESC)
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_message_chat_createdAt" ON "message" ("chatId", "createdAt" DESC)
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_message_sender_createdAt" ON "message" ("senderId", "createdAt" DESC)
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_message_read_status_user_readAt" ON "message_read_status" ("userId", "readAt")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_chat_participants_chatId" ON "chat_participants" ("chatId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_chat_participants_userId" ON "chat_participants" ("userId")
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "message" 
      ADD CONSTRAINT "FK_message_chat" 
      FOREIGN KEY ("chatId") REFERENCES "chat"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "message" 
      ADD CONSTRAINT "FK_message_sender" 
      FOREIGN KEY ("senderId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "message_read_status" 
      ADD CONSTRAINT "FK_message_read_status_message" 
      FOREIGN KEY ("messageId") REFERENCES "message"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "message_read_status" 
      ADD CONSTRAINT "FK_message_read_status_user" 
      FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_participants" 
      ADD CONSTRAINT "FK_chat_participants_chat" 
      FOREIGN KEY ("chatId") REFERENCES "chat"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "chat_participants" 
      ADD CONSTRAINT "FK_chat_participants_user" 
      FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);

    // Add check constraints for enums
    await queryRunner.query(`
      ALTER TABLE "chat" 
      ADD CONSTRAINT "CHK_chat_type" 
      CHECK ("type" IN ('direct', 'group'))
    `);

    await queryRunner.query(`
      ALTER TABLE "message" 
      ADD CONSTRAINT "CHK_message_type" 
      CHECK ("type" IN ('text', 'system'))
    `);

    await queryRunner.query(`
      ALTER TABLE "message" 
      ADD CONSTRAINT "CHK_message_status" 
      CHECK ("status" IN ('sent', 'delivered', 'read'))
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "chat_participants" DROP CONSTRAINT "FK_chat_participants_user"`);
    await queryRunner.query(`ALTER TABLE "chat_participants" DROP CONSTRAINT "FK_chat_participants_chat"`);
    await queryRunner.query(`ALTER TABLE "message_read_status" DROP CONSTRAINT "FK_message_read_status_user"`);
    await queryRunner.query(`ALTER TABLE "message_read_status" DROP CONSTRAINT "FK_message_read_status_message"`);
    await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_message_sender"`);
    await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_message_chat"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_chat_participants_userId"`);
    await queryRunner.query(`DROP INDEX "IDX_chat_participants_chatId"`);
    await queryRunner.query(`DROP INDEX "IDX_message_read_status_user_readAt"`);
    await queryRunner.query(`DROP INDEX "IDX_message_sender_createdAt"`);
    await queryRunner.query(`DROP INDEX "IDX_message_chat_createdAt"`);
    await queryRunner.query(`DROP INDEX "IDX_chat_lastMessageAt"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "chat_participants"`);
    await queryRunner.query(`DROP TABLE "message_read_status"`);
    await queryRunner.query(`DROP TABLE "message"`);
    await queryRunner.query(`DROP TABLE "chat"`);
  }
}

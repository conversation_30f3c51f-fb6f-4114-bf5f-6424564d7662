import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Message } from './message.entity';

export enum ChatType {
  DIRECT = 'direct',
  GROUP = 'group',
}

@Entity()
export class Chat {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: ChatType,
    default: ChatType.DIRECT,
  })
  type: ChatType;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  description: string;

  @ManyToMany(() => User, { eager: true })
  @JoinTable({
    name: 'chat_participants',
    joinColumn: { name: 'chatId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'userId', referencedColumnName: 'id' },
  })
  participants: User[];

  @OneToMany(() => Message, (message) => message.chat, { cascade: true })
  messages: Message[];

  @Column({ type: 'timestamp', nullable: true })
  lastMessageAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual property to get the other participant in a direct chat
  getOtherParticipant(currentUserId: number): User | null {
    if (this.type !== ChatType.DIRECT || this.participants.length !== 2) {
      return null;
    }
    return this.participants.find(p => p.id !== currentUserId) || null;
  }

  // Virtual property to check if user is participant
  isParticipant(userId: number): boolean {
    return this.participants.some(p => p.id === userId);
  }
}

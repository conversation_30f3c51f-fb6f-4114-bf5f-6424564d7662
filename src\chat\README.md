# Real-Time Chat System

A comprehensive real-time chat system built with NestJS, Socket.IO, and TypeORM.

## Features

- **Real-time messaging** using WebSocket connections
- **Direct and group chats** with participant management
- **Message persistence** with read status tracking
- **Cursor-based pagination** for efficient message loading
- **Rate limiting** to prevent spam
- **Input sanitization** for security
- **Authentication integration** with existing JWT system

## API Endpoints

### Chat Management

#### Create Chat
```http
POST /api/chats
Content-Type: application/json
Authorization: Bearer <token>

{
  "type": "direct",
  "participantIds": [2]
}
```

#### Get User Chats
```http
GET /api/chats?limit=20&cursor=uuid
Authorization: Bearer <token>
```

#### Get Chat Messages
```http
GET /api/chats/:chatId/messages?limit=50&cursor=uuid
Authorization: Bearer <token>
```

#### Send Message
```http
POST /api/chats/:chatId/messages
Content-Type: application/json
Authorization: Bearer <token>

{
  "content": "Hello, how are you?",
  "type": "text"
}
```

#### Mark Messages as Read
```http
PUT /api/chats/:chatId/read
Authorization: Bearer <token>
```

## WebSocket Events

### Connection
Connect to the chat namespace with authentication:
```javascript
const socket = io('/chat', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### Events

#### Join Chat Room
```javascript
socket.emit('join_chat', { chatId: 'chat-uuid' });
```

#### Send Message
```javascript
socket.emit('send_message', {
  chatId: 'chat-uuid',
  message: {
    content: 'Hello!',
    type: 'text'
  }
});
```

#### Mark Messages as Read
```javascript
socket.emit('mark_read', { chatId: 'chat-uuid' });
```

#### Typing Indicators
```javascript
// Start typing
socket.emit('typing_start', { chatId: 'chat-uuid' });

// Stop typing
socket.emit('typing_stop', { chatId: 'chat-uuid' });
```

### Listening for Events

#### New Message
```javascript
socket.on('new_message', (data) => {
  console.log('New message:', data.message);
});
```

#### Messages Read
```javascript
socket.on('messages_read', (data) => {
  console.log(`${data.markedCount} messages marked as read`);
});
```

#### User Typing
```javascript
socket.on('user_typing', (data) => {
  console.log(`${data.username} is ${data.isTyping ? 'typing' : 'not typing'}`);
});
```

## Database Schema

### Chat Entity
- `id` (UUID) - Primary key
- `type` (enum) - 'direct' or 'group'
- `name` (string, nullable) - Chat name (required for groups)
- `description` (string, nullable) - Chat description
- `lastMessageAt` (timestamp, nullable) - Last message timestamp
- `createdAt` (timestamp) - Creation timestamp
- `updatedAt` (timestamp) - Last update timestamp

### Message Entity
- `id` (UUID) - Primary key
- `content` (text) - Message content (sanitized)
- `type` (enum) - 'text' or 'system'
- `status` (enum) - 'sent', 'delivered', 'read'
- `editedAt` (timestamp, nullable) - Edit timestamp
- `createdAt` (timestamp) - Creation timestamp
- `updatedAt` (timestamp) - Last update timestamp

### MessageReadStatus Entity
- `id` (UUID) - Primary key
- `readAt` (timestamp, nullable) - When message was read
- `createdAt` (timestamp) - Creation timestamp

## Security Features

1. **Authentication**: JWT token validation for all operations
2. **Authorization**: Users can only access chats they participate in
3. **Input Sanitization**: All message content is sanitized using DOMPurify
4. **Rate Limiting**: 
   - WebSocket: 10 messages per minute per user
   - HTTP: 30 messages per minute per user
5. **Validation**: Comprehensive input validation using class-validator

## Performance Optimizations

1. **Database Indexes**: Optimized queries with proper indexing
2. **Cursor-based Pagination**: Efficient pagination for large datasets
3. **Lazy Loading**: Messages loaded on demand
4. **Connection Management**: Efficient WebSocket connection tracking

## Usage Examples

### Frontend Integration (React/Vue/Angular)

```javascript
import io from 'socket.io-client';

class ChatService {
  constructor(token) {
    this.socket = io('/chat', {
      auth: { token }
    });
    
    this.setupEventListeners();
  }
  
  setupEventListeners() {
    this.socket.on('connected', (data) => {
      console.log('Connected to chat:', data);
    });
    
    this.socket.on('new_message', (data) => {
      this.handleNewMessage(data);
    });
    
    this.socket.on('user_typing', (data) => {
      this.handleTypingIndicator(data);
    });
  }
  
  joinChat(chatId) {
    this.socket.emit('join_chat', { chatId });
  }
  
  sendMessage(chatId, content) {
    this.socket.emit('send_message', {
      chatId,
      message: { content, type: 'text' }
    });
  }
  
  markAsRead(chatId) {
    this.socket.emit('mark_read', { chatId });
  }
}
```

## Error Handling

The system provides comprehensive error handling:

- **Authentication errors**: Invalid or missing JWT tokens
- **Authorization errors**: Access to unauthorized chats
- **Validation errors**: Invalid input data
- **Rate limiting errors**: Too many requests
- **Not found errors**: Non-existent chats or messages

All errors include meaningful messages and appropriate HTTP status codes.

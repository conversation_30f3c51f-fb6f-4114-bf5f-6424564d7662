# Real-Time Chat System

A comprehensive real-time chat system built with NestJS, Socket.IO, and TypeORM.

## Features

- **Real-time messaging** using WebSocket connections
- **Direct and group chats** with participant management
- **Message persistence** with read status tracking
- **Cursor-based pagination** for efficient message loading
- **Rate limiting** to prevent spam
- **Input sanitization** for security
- **Authentication integration** with existing JWT system

## API Endpoints

### Chat Management

#### Create Chat
```http
POST /api/chats
Content-Type: application/json
Authorization: Bearer <token>

{
  "type": "direct",
  "participantIds": [2]
}
```

#### Get User Chats
```http
GET /api/chats?limit=20&cursor=uuid
Authorization: Bearer <token>
```

#### Get Chat Messages
```http
GET /api/chats/:chatId/messages?limit=50&cursor=uuid
Authorization: Bearer <token>
```

#### Send Message
```http
POST /api/chats/:chatId/messages
Content-Type: application/json
Authorization: Bearer <token>

{
  "content": "Hello, how are you?",
  "type": "text"
}
```

#### Mark Messages as Read
```http
PUT /api/chats/:chatId/read
Authorization: Bearer <token>
```

## WebSocket Events

### Connection
Connect to the chat namespace with authentication:
```javascript
const socket = io('/chat', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### Events

#### Join Chat Room
```javascript
socket.emit('join_chat', { chatId: 'chat-uuid' });
```

#### Send Message
```javascript
socket.emit('send_message', {
  chatId: 'chat-uuid',
  message: {
    content: 'Hello!',
    type: 'text'
  }
});
```

#### Mark Messages as Read
```javascript
socket.emit('mark_read', { chatId: 'chat-uuid' });
```

#### Typing Indicators
```javascript
// Start typing
socket.emit('typing_start', { chatId: 'chat-uuid' });

// Stop typing
socket.emit('typing_stop', { chatId: 'chat-uuid' });
```

### Listening for Events

#### New Message
```javascript
socket.on('new_message', (data) => {
  console.log('New message:', data.message);
});
```

#### Messages Read
```javascript
socket.on('messages_read', (data) => {
  console.log(`${data.markedCount} messages marked as read`);
});
```

#### User Typing
```javascript
socket.on('user_typing', (data) => {
  console.log(`${data.username} is ${data.isTyping ? 'typing' : 'not typing'}`);
});
```

## Database Schema

### Chat Entity
- `id` (UUID) - Primary key
- `type` (enum) - 'direct' or 'group'
- `name` (string, nullable) - Chat name (required for groups)
- `description` (string, nullable) - Chat description
- `lastMessageAt` (timestamp, nullable) - Last message timestamp
- `createdAt` (timestamp) - Creation timestamp
- `updatedAt` (timestamp) - Last update timestamp

### Message Entity
- `id` (UUID) - Primary key
- `content` (text) - Message content (sanitized)
- `type` (enum) - 'text' or 'system'
- `status` (enum) - 'sent', 'delivered', 'read'
- `editedAt` (timestamp, nullable) - Edit timestamp
- `createdAt` (timestamp) - Creation timestamp
- `updatedAt` (timestamp) - Last update timestamp

### MessageReadStatus Entity
- `id` (UUID) - Primary key
- `readAt` (timestamp, nullable) - When message was read
- `createdAt` (timestamp) - Creation timestamp

## Security Features

1. **Authentication**: JWT token validation for all operations
2. **Authorization**: Users can only access chats they participate in
3. **Input Sanitization**: All message content is sanitized using DOMPurify
4. **Rate Limiting**:
   - WebSocket: 10 messages per minute per user
   - HTTP: 30 messages per minute per user
5. **Validation**: Comprehensive input validation using class-validator

## Performance Optimizations

1. **Database Indexes**: Optimized queries with proper indexing
2. **Cursor-based Pagination**: Efficient pagination for large datasets
3. **Lazy Loading**: Messages loaded on demand
4. **Connection Management**: Efficient WebSocket connection tracking

## Usage Examples

### Frontend Integration (React/Vue/Angular)

```javascript
import io from 'socket.io-client';

class ChatService {
  constructor(token) {
    this.socket = io('/chat', {
      auth: { token }
    });

    this.setupEventListeners();
  }

  setupEventListeners() {
    this.socket.on('connected', (data) => {
      console.log('Connected to chat:', data);
      // User is automatically joined to all their chat rooms
    });

    this.socket.on('new_message', (data) => {
      console.log('New message received:', data);
      this.handleNewMessage(data);
    });

    this.socket.on('messages_read', (data) => {
      console.log('Messages marked as read:', data);
      this.handleMessagesRead(data);
    });

    this.socket.on('user_typing', (data) => {
      this.handleTypingIndicator(data);
    });

    this.socket.on('error', (error) => {
      console.error('Chat error:', error);
    });
  }

  // Optional: Manually join a specific chat (users are auto-joined on connect)
  joinChat(chatId) {
    this.socket.emit('join_chat', { chatId });
  }

  // Send message via WebSocket (real-time)
  sendMessage(chatId, content) {
    this.socket.emit('send_message', {
      chatId,
      message: { content, type: 'text' }
    });
  }

  // Send message via HTTP (with fallback)
  async sendMessageHTTP(chatId, content) {
    try {
      const response = await fetch(`/api/chats/${chatId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          content,
          type: 'text'
        })
      });
      return await response.json();
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }

  markAsRead(chatId) {
    this.socket.emit('mark_read', { chatId });
  }

  startTyping(chatId) {
    this.socket.emit('typing_start', { chatId });
  }

  stopTyping(chatId) {
    this.socket.emit('typing_stop', { chatId });
  }

  handleNewMessage(data) {
    // Update your UI with the new message
    // data.message contains: id, content, type, sender, createdAt, isRead
    console.log(`New message in chat ${data.chatId}:`, data.message);
  }

  handleMessagesRead(data) {
    // Update read status in your UI
    console.log(`${data.markedCount} messages marked as read by user ${data.userId}`);
  }

  handleTypingIndicator(data) {
    // Show/hide typing indicator
    if (data.isTyping) {
      console.log(`${data.username} is typing...`);
    } else {
      console.log(`${data.username} stopped typing`);
    }
  }
}

// Usage example
const token = 'your-jwt-token';
const chatService = new ChatService(token);

// The user will automatically receive messages in real-time
// No need to manually join chats - it's done automatically on connection
```

### React Hook Example

```javascript
import { useEffect, useState } from 'react';
import io from 'socket.io-client';

export function useChat(token) {
  const [socket, setSocket] = useState(null);
  const [messages, setMessages] = useState({});
  const [typingUsers, setTypingUsers] = useState({});

  useEffect(() => {
    if (!token) return;

    const newSocket = io('/chat', {
      auth: { token }
    });

    newSocket.on('connected', (data) => {
      console.log('Connected to chat system');
    });

    newSocket.on('new_message', (data) => {
      setMessages(prev => ({
        ...prev,
        [data.chatId]: [...(prev[data.chatId] || []), data.message]
      }));
    });

    newSocket.on('user_typing', (data) => {
      setTypingUsers(prev => ({
        ...prev,
        [data.chatId]: {
          ...prev[data.chatId],
          [data.userId]: data.isTyping
        }
      }));
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [token]);

  const sendMessage = (chatId, content) => {
    if (socket) {
      socket.emit('send_message', {
        chatId,
        message: { content, type: 'text' }
      });
    }
  };

  const markAsRead = (chatId) => {
    if (socket) {
      socket.emit('mark_read', { chatId });
    }
  };

  return {
    socket,
    messages,
    typingUsers,
    sendMessage,
    markAsRead
  };
}
```

## Error Handling

The system provides comprehensive error handling:

- **Authentication errors**: Invalid or missing JWT tokens
- **Authorization errors**: Access to unauthorized chats
- **Validation errors**: Invalid input data
- **Rate limiting errors**: Too many requests
- **Not found errors**: Non-existent chats or messages

All errors include meaningful messages and appropriate HTTP status codes.

import { CanActivate, ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';

interface AuthenticatedSocket extends Socket {
  userId?: number;
  username?: string;
}

@Injectable()
export class WsThrottlerGuard implements CanActivate {
  private readonly logger = new Logger(WsThrottlerGuard.name);
  private readonly messageTimestamps = new Map<number, number[]>(); // userId -> timestamps
  private readonly MESSAGE_LIMIT = 10; // messages per minute
  private readonly TIME_WINDOW = 60 * 1000; // 1 minute in milliseconds

  canActivate(context: ExecutionContext): boolean {
    const client: AuthenticatedSocket = context.switchToWs().getClient();
    const userId = client.userId;

    if (!userId) {
      return true; // Let auth guard handle this
    }

    const now = Date.now();
    const userTimestamps = this.messageTimestamps.get(userId) || [];

    // Remove timestamps older than the time window
    const recentTimestamps = userTimestamps.filter(
      timestamp => now - timestamp < this.TIME_WINDOW
    );

    if (recentTimestamps.length >= this.MESSAGE_LIMIT) {
      this.logger.warn(
        `Rate limit exceeded for user ${userId}. ${recentTimestamps.length} messages in the last minute.`
      );
      throw new WsException('Rate limit exceeded. Please slow down.');
    }

    // Add current timestamp and update the map
    recentTimestamps.push(now);
    this.messageTimestamps.set(userId, recentTimestamps);

    return true;
  }
}

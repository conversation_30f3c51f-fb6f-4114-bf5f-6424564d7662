/**
 * Example integration test for the Chat System
 * This file demonstrates how to test the chat functionality
 * 
 * To run this example:
 * 1. Ensure your database is running
 * 2. Start the server: npm run start:dev
 * 3. Use this code as reference for testing
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { ChatModule } from './chat.module';
import { UserModule } from '../user/user.module';
import { AuthModule } from '../auth/auth.module';

describe('Chat System Integration (Example)', () => {
  let app: INestApplication;
  let authToken: string;
  let userId: number;
  let chatId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ isGlobal: true }),
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT) || 5432,
          username: process.env.DB_USERNAME || 'postgres',
          password: process.env.DB_PASSWORD || 'password',
          database: process.env.DB_NAME || 'test_db',
          entities: ['src/**/*.entity{.ts,.js}'],
          synchronize: true, // Only for testing
        }),
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-secret',
          signOptions: { expiresIn: '24h' },
        }),
        AuthModule,
        UserModule,
        ChatModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Create test users and get auth token
    // This is a simplified example - in real tests you'd create proper test data
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123',
      });

    authToken = loginResponse.body.access_token;
    userId = loginResponse.body.user.id;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Chat Creation', () => {
    it('should create a direct chat', async () => {
      const response = await request(app.getHttpServer())
        .post('/chats')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'direct',
          participantIds: [2], // Another user ID
        })
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.type).toBe('direct');
      expect(response.body.participants).toHaveLength(2);
      
      chatId = response.body.id;
    });

    it('should create a group chat', async () => {
      const response = await request(app.getHttpServer())
        .post('/chats')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          type: 'group',
          name: 'Test Group',
          description: 'A test group chat',
          participantIds: [2, 3], // Other user IDs
        })
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.type).toBe('group');
      expect(response.body.name).toBe('Test Group');
      expect(response.body.participants).toHaveLength(3); // Including creator
    });
  });

  describe('Message Operations', () => {
    it('should send a message to chat', async () => {
      const response = await request(app.getHttpServer())
        .post(`/chats/${chatId}/messages`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: 'Hello, this is a test message!',
          type: 'text',
        })
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.content).toBe('Hello, this is a test message!');
      expect(response.body.sender.id).toBe(userId);
    });

    it('should get chat messages', async () => {
      const response = await request(app.getHttpServer())
        .get(`/chats/${chatId}/messages`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('messages');
      expect(response.body).toHaveProperty('hasMore');
      expect(Array.isArray(response.body.messages)).toBe(true);
    });

    it('should mark messages as read', async () => {
      const response = await request(app.getHttpServer())
        .put(`/chats/${chatId}/read`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('markedCount');
      expect(typeof response.body.markedCount).toBe('number');
    });
  });

  describe('Chat Retrieval', () => {
    it('should get user chats', async () => {
      const response = await request(app.getHttpServer())
        .get('/chats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('chats');
      expect(response.body).toHaveProperty('hasMore');
      expect(Array.isArray(response.body.chats)).toBe(true);
    });

    it('should get unread count', async () => {
      const response = await request(app.getHttpServer())
        .get(`/chats/${chatId}/unread-count`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('unreadCount');
      expect(typeof response.body.unreadCount).toBe('number');
    });
  });

  describe('Error Handling', () => {
    it('should reject unauthorized requests', async () => {
      await request(app.getHttpServer())
        .get('/chats')
        .expect(401);
    });

    it('should reject access to non-existent chat', async () => {
      await request(app.getHttpServer())
        .get('/chats/non-existent-uuid/messages')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    it('should validate message content', async () => {
      await request(app.getHttpServer())
        .post(`/chats/${chatId}/messages`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          content: '', // Empty content should be rejected
          type: 'text',
        })
        .expect(400);
    });
  });
});

/**
 * WebSocket Testing Example
 * 
 * For WebSocket testing, you can use socket.io-client:
 */

/*
import { io, Socket } from 'socket.io-client';

describe('Chat WebSocket (Example)', () => {
  let clientSocket: Socket;

  beforeAll((done) => {
    clientSocket = io('http://localhost:3000/chat', {
      auth: {
        token: authToken
      }
    });
    
    clientSocket.on('connected', () => {
      done();
    });
  });

  afterAll(() => {
    clientSocket.close();
  });

  it('should join a chat room', (done) => {
    clientSocket.emit('join_chat', { chatId });
    
    clientSocket.on('joined_chat', (data) => {
      expect(data.chatId).toBe(chatId);
      done();
    });
  });

  it('should send and receive messages', (done) => {
    const testMessage = {
      content: 'WebSocket test message',
      type: 'text'
    };

    clientSocket.on('new_message', (data) => {
      expect(data.message.content).toBe(testMessage.content);
      done();
    });

    clientSocket.emit('send_message', {
      chatId,
      message: testMessage
    });
  });
});
*/
